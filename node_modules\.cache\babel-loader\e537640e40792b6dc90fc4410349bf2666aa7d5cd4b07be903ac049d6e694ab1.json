{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Pill = createLucideIcon(\"Pill\", [[\"path\", {\n  d: \"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z\",\n  key: \"wa1lgi\"\n}], [\"path\", {\n  d: \"m8.5 8.5 7 7\",\n  key: \"rvfmvr\"\n}]]);\nexport { Pill as default };", "map": {"version": 3, "names": ["<PERSON>ll", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\pill.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pill\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAuNSAyMC41IDEwLTEwYTQuOTUgNC45NSAwIDEgMC03LTdsLTEwIDEwYTQuOTUgNC45NSAwIDEgMCA3IDdaIiAvPgogIDxwYXRoIGQ9Im04LjUgOC41IDcgNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pill\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pill = createLucideIcon('Pill', [\n  [\n    'path',\n    {\n      d: 'm10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z',\n      key: 'wa1lgi',\n    },\n  ],\n  ['path', { d: 'm8.5 8.5 7 7', key: 'rvfmvr' }],\n]);\n\nexport default Pill;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}