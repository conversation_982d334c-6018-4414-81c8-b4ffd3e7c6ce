[{"C:\\Users\\<USER>\\Desktop\\projetest2\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\context\\AppContext.tsx": "3", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Lesson\\LessonView.tsx": "4", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\Onboarding.tsx": "5", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\Header.tsx": "7", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Lesson\\ExerciseView.tsx": "8", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\LanguageStep.tsx": "9", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\NameStep.tsx": "10", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\data\\mockData.ts": "11", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\WelcomeStep.tsx": "12", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\GoalsStep.tsx": "13", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\SkillLevelStep.tsx": "14", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\CompletionStep.tsx": "15", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\StatsPanel.tsx": "16", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\ProgressSection.tsx": "17", "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\LessonMap.tsx": "18"}, {"size": 273, "mtime": 1754147542552, "results": "19", "hashOfConfig": "20"}, {"size": 953, "mtime": 1754147592922, "results": "21", "hashOfConfig": "20"}, {"size": 2590, "mtime": 1754147586136, "results": "22", "hashOfConfig": "20"}, {"size": 7016, "mtime": 1754147933364, "results": "23", "hashOfConfig": "20"}, {"size": 4589, "mtime": 1754147628050, "results": "24", "hashOfConfig": "20"}, {"size": 2255, "mtime": 1754147786340, "results": "25", "hashOfConfig": "20"}, {"size": 5148, "mtime": 1754148090368, "results": "26", "hashOfConfig": "20"}, {"size": 7673, "mtime": 1754147969436, "results": "27", "hashOfConfig": "20"}, {"size": 4533, "mtime": 1754147681756, "results": "28", "hashOfConfig": "20"}, {"size": 3114, "mtime": 1754147661686, "results": "29", "hashOfConfig": "20"}, {"size": 2917, "mtime": 1754147574038, "results": "30", "hashOfConfig": "20"}, {"size": 2760, "mtime": 1754147645362, "results": "31", "hashOfConfig": "20"}, {"size": 6942, "mtime": 1754147737260, "results": "32", "hashOfConfig": "20"}, {"size": 5222, "mtime": 1754147706867, "results": "33", "hashOfConfig": "20"}, {"size": 6035, "mtime": 1754147766682, "results": "34", "hashOfConfig": "20"}, {"size": 7068, "mtime": 1754147895549, "results": "35", "hashOfConfig": "20"}, {"size": 4737, "mtime": 1754147832285, "results": "36", "hashOfConfig": "20"}, {"size": 5890, "mtime": 1754147863895, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ioecdn", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\context\\AppContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Lesson\\LessonView.tsx", ["92"], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\Onboarding.tsx", ["93"], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Lesson\\ExerciseView.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\LanguageStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\NameStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\data\\mockData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\WelcomeStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\GoalsStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\SkillLevelStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Onboarding\\CompletionStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\StatsPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\ProgressSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\projetest2\\src\\components\\Dashboard\\LessonMap.tsx", [], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 4, "column": 21, "nodeType": "96", "messageId": "97", "endLine": 4, "endColumn": 31}, {"ruleId": "94", "severity": 1, "message": "98", "line": 4, "column": 26, "nodeType": "96", "messageId": "97", "endLine": 4, "endColumn": 36}, "@typescript-eslint/no-unused-vars", "'ArrowRight' is defined but never used.", "Identifier", "unusedVar", "'SkillLevel' is defined but never used."]