{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.mjs';\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    children,\n    ...rest\n  }, ref) => createElement(\"svg\", {\n    ref,\n    ...defaultAttributes,\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n    className: `lucide lucide-${toKebabCase(iconName)}`,\n    ...rest\n  }, [...iconNode.map(([tag, attrs]) => createElement(tag, attrs)), ...((Array.isArray(children) ? children : [children]) || [])]));\n  Component.displayName = `${iconName}`;\n  return Component;\n};\nvar createLucideIcon$1 = createLucideIcon;\nexport { createLucideIcon$1 as default, toKebabCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "children", "rest", "ref", "createElement", "defaultAttributes", "width", "height", "stroke", "Number", "className", "map", "tag", "attrs", "Array", "isArray", "displayName", "createLucideIcon$1"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import { forwardRef, createElement, ReactSVG, SVGProps, ForwardRefExoticComponent, RefAttributes } from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][]\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number\n  absoluteStrokeWidth?: boolean\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) => string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: `lucide lucide-${toKebabCase(iconName)}`,\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(\n            (Array.isArray(children) ? children : [children]) || []\n          )\n        ],\n      ),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon\n"], "mappings": ";;;;;;AAsBa,MAAAA,WAAA,GAAeC,MAAmB,IAAAA,MAAA,CAAOC,OAAA,CAAQ,oBAAsB,SAAO,EAAEC,WAAY;AAEzG,MAAMC,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAmC;EAC7E,MAAMC,SAAY,GAAAC,UAAA,CAChB,CAAC;IAAEC,KAAQ;IAAgBC,IAAO;IAAIC,WAAc;IAAGC,mBAAqB;IAAAC,QAAA;IAAA,GAAaC;EAAK,GAAGC,GAC/F,KAAAC,aAAA,CACE,OACA;IACED,GAAA;IACA,GAAGE,iBAAA;IACHC,KAAO,EAAAR,IAAA;IACPS,MAAQ,EAAAT,IAAA;IACRU,MAAQ,EAAAX,KAAA;IACRE,WAAA,EAAaC,mBAAA,GAAsBS,MAAO,CAAAV,WAAW,IAAI,EAAK,GAAAU,MAAA,CAAOX,IAAI,CAAI,GAAAC,WAAA;IAC7EW,SAAA,EAAW,iBAAiBtB,WAAA,CAAYK,QAAQ;IAChD,GAAGS;EACL,GACA,CACE,GAAGR,QAAS,CAAAiB,GAAA,CAAI,CAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,KAAAT,aAAA,CAAcQ,GAAK,EAAAC,KAAK,CAAC,GAC3D,KACGC,KAAA,CAAMC,OAAQ,CAAAd,QAAQ,IAAIA,QAAW,IAACA,QAAQ,MAAM,EAAC,EAG5D,EACJ;EAEAN,SAAA,CAAUqB,WAAA,GAAc,GAAGvB,QAAA;EAEpB,OAAAE,SAAA;AACT;AAEA,IAAAsB,kBAAA,GAAezB,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}