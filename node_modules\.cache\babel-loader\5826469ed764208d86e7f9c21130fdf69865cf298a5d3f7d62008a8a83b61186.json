{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ZapOff = createLucideIcon(\"ZapOff\", [[\"polyline\", {\n  points: \"12.41 6.75 13 2 10.57 4.92\",\n  key: \"122m05\"\n}], [\"polyline\", {\n  points: \"18.57 12.91 21 10 15.66 10\",\n  key: \"16r43o\"\n}], [\"polyline\", {\n  points: \"8 8 3 14 12 14 11 22 16 16\",\n  key: \"tmh4bc\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]]);\nexport { ZapOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\zap-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ZapOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxMi40MSA2Ljc1IDEzIDIgMTAuNTcgNC45MiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxOC41NyAxMi45MSAyMSAxMCAxNS42NiAxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSI4IDggMyAxNCAxMiAxNCAxMSAyMiAxNiAxNiIgLz4KICA8bGluZSB4MT0iMiIgeDI9IjIyIiB5MT0iMiIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ZapOff = createLucideIcon('ZapOff', [\n  ['polyline', { points: '12.41 6.75 13 2 10.57 4.92', key: '122m05' }],\n  ['polyline', { points: '18.57 12.91 21 10 15.66 10', key: '16r43o' }],\n  ['polyline', { points: '8 8 3 14 12 14 11 22 16 16', key: 'tmh4bc' }],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default ZapOff;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,UAAY;EAAEC,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAED,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAED,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}