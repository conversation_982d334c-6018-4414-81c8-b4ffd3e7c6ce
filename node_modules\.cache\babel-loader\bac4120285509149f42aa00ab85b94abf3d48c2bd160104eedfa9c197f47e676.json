{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst SmartphoneNfc = createLucideIcon(\"SmartphoneNfc\", [[\"rect\", {\n  width: \"7\",\n  height: \"12\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"5nje8w\"\n}], [\"path\", {\n  d: \"M13 8.32a7.43 7.43 0 0 1 0 7.36\",\n  key: \"1g306n\"\n}], [\"path\", {\n  d: \"M16.46 6.21a11.76 11.76 0 0 1 0 11.58\",\n  key: \"uqvjvo\"\n}], [\"path\", {\n  d: \"M19.91 4.1a15.91 15.91 0 0 1 .01 15.8\",\n  key: \"ujntz3\"\n}]]);\nexport { SmartphoneNfc as default };", "map": {"version": 3, "names": ["SmartphoneNfc", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\smartphone-nfc.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SmartphoneNfc\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIxMiIgeD0iMiIgeT0iNiIgcng9IjEiIC8+CiAgPHBhdGggZD0iTTEzIDguMzJhNy40MyA3LjQzIDAgMCAxIDAgNy4zNiIgLz4KICA8cGF0aCBkPSJNMTYuNDYgNi4yMWExMS43NiAxMS43NiAwIDAgMSAwIDExLjU4IiAvPgogIDxwYXRoIGQ9Ik0xOS45MSA0LjFhMTUuOTEgMTUuOTEgMCAwIDEgLjAxIDE1LjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/smartphone-nfc\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SmartphoneNfc = createLucideIcon('SmartphoneNfc', [\n  [\n    'rect',\n    { width: '7', height: '12', x: '2', y: '6', rx: '1', key: '5nje8w' },\n  ],\n  ['path', { d: 'M13 8.32a7.43 7.43 0 0 1 0 7.36', key: '1g306n' }],\n  ['path', { d: 'M16.46 6.21a11.76 11.76 0 0 1 0 11.58', key: 'uqvjvo' }],\n  ['path', { d: 'M19.91 4.1a15.91 15.91 0 0 1 .01 15.8', key: 'ujntz3' }],\n]);\n\nexport default SmartphoneNfc;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}