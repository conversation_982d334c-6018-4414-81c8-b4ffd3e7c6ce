import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Settings, User, LogOut, Crown, Flame } from 'lucide-react';
import { useApp } from '../../context/AppContext';

const Header: React.FC = () => {
  const { state, dispatch } = useApp();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = () => {
    dispatch({ type: 'RESET_APP' });
  };

  if (!state.user) return null;

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center gap-3"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">C</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-800">CodeLearn</h1>
              <p className="text-xs text-gray-500">Yazılım Dillerini Öğren</p>
            </div>
          </motion.div>

          {/* Center - Current Language */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="hidden md:flex items-center gap-3 bg-gray-50 rounded-xl px-4 py-2"
          >
            <span className="text-2xl">{state.currentLanguage?.icon}</span>
            <div>
              <p className="font-semibold text-gray-800">{state.currentLanguage?.name}</p>
              <p className="text-xs text-gray-500">Aktif Dil</p>
            </div>
          </motion.div>

          {/* Right Side - Stats & User */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex items-center gap-4"
          >
            {/* Streak */}
            <div className="hidden sm:flex items-center gap-2 bg-orange-50 text-orange-600 px-3 py-2 rounded-lg">
              <Flame className="w-4 h-4" />
              <span className="font-semibold">{state.user.streak}</span>
            </div>

            {/* XP */}
            <div className="hidden sm:flex items-center gap-2 bg-yellow-50 text-yellow-600 px-3 py-2 rounded-lg">
              <Crown className="w-4 h-4" />
              <span className="font-semibold">{state.user.totalXP} XP</span>
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 rounded-xl px-3 py-2 transition-colors duration-200"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="hidden sm:block font-medium text-gray-700">
                  {state.user.name}
                </span>
                {state.user.isGuest && (
                  <span className="hidden sm:block text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                    Misafir
                  </span>
                )}
              </button>

              {/* Dropdown Menu */}
              {showUserMenu && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50"
                >
                  <button className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-gray-700">
                    <Settings className="w-4 h-4" />
                    Ayarlar
                  </button>
                  
                  {state.user.isGuest && (
                    <button className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-blue-600">
                      <User className="w-4 h-4" />
                      Hesap Oluştur
                    </button>
                  )}
                  
                  <hr className="my-2" />
                  
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-red-600"
                  >
                    <LogOut className="w-4 h-4" />
                    Çıkış Yap
                  </button>
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </header>
  );
};

export default Header;
