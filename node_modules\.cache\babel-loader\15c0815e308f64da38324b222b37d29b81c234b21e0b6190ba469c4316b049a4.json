{"ast": null, "code": "import { createRenderStep } from './render-step.mjs';\nconst stepsOrder = [\"prepare\", \"read\", \"update\", \"preRender\", \"render\", \"postRender\"];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n  let runNextFrame = false;\n  let useDefaultElapsed = true;\n  const state = {\n    delta: 0,\n    timestamp: 0,\n    isProcessing: false\n  };\n  const steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => runNextFrame = true);\n    return acc;\n  }, {});\n  const processStep = stepId => steps[stepId].process(state);\n  const processBatch = () => {\n    const timestamp = performance.now();\n    runNextFrame = false;\n    state.delta = useDefaultElapsed ? 1000 / 60 : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n    state.timestamp = timestamp;\n    state.isProcessing = true;\n    stepsOrder.forEach(processStep);\n    state.isProcessing = false;\n    if (runNextFrame && allowKeepAlive) {\n      useDefaultElapsed = false;\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const wake = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!state.isProcessing) {\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const schedule = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n      if (!runNextFrame) wake();\n      return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n  }, {});\n  const cancel = process => stepsOrder.forEach(key => steps[key].cancel(process));\n  return {\n    schedule,\n    cancel,\n    state,\n    steps\n  };\n}\nexport { createRenderBatcher, stepsOrder };", "map": {"version": 3, "names": ["createRenderStep", "stepsOrder", "maxElapsed", "createRenderBatcher", "scheduleNextBatch", "allowKeepAlive", "runNextFrame", "useDefaultElapsed", "state", "delta", "timestamp", "isProcessing", "steps", "reduce", "acc", "key", "processStep", "stepId", "process", "processBatch", "performance", "now", "Math", "max", "min", "for<PERSON>ach", "wake", "schedule", "step", "keepAlive", "immediate", "cancel"], "sources": ["C:/Users/<USER>/Desktop/projetest2/node_modules/framer-motion/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { createRenderStep } from './render-step.mjs';\n\nconst stepsOrder = [\n    \"prepare\",\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0,\n        timestamp: 0,\n        isProcessing: false,\n    };\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(() => (runNextFrame = true));\n        return acc;\n    }, {});\n    const processStep = (stepId) => steps[stepId].process(state);\n    const processBatch = () => {\n        const timestamp = performance.now();\n        runNextFrame = false;\n        state.delta = useDefaultElapsed\n            ? 1000 / 60\n            : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        stepsOrder.forEach(processStep);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => stepsOrder.forEach((key) => steps[key].cancel(process));\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher, stepsOrder };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mBAAmB;AAEpD,MAAMC,UAAU,GAAG,CACf,SAAS,EACT,MAAM,EACN,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,YAAY,CACf;AACD,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,mBAAmBA,CAACC,iBAAiB,EAAEC,cAAc,EAAE;EAC5D,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,MAAMC,KAAK,GAAG;IACVC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAClB,CAAC;EACD,MAAMC,KAAK,GAAGX,UAAU,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC1CD,GAAG,CAACC,GAAG,CAAC,GAAGf,gBAAgB,CAAC,MAAOM,YAAY,GAAG,IAAK,CAAC;IACxD,OAAOQ,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAME,WAAW,GAAIC,MAAM,IAAKL,KAAK,CAACK,MAAM,CAAC,CAACC,OAAO,CAACV,KAAK,CAAC;EAC5D,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMT,SAAS,GAAGU,WAAW,CAACC,GAAG,CAAC,CAAC;IACnCf,YAAY,GAAG,KAAK;IACpBE,KAAK,CAACC,KAAK,GAAGF,iBAAiB,GACzB,IAAI,GAAG,EAAE,GACTe,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACd,SAAS,GAAGF,KAAK,CAACE,SAAS,EAAER,UAAU,CAAC,EAAE,CAAC,CAAC;IACpEM,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAACG,YAAY,GAAG,IAAI;IACzBV,UAAU,CAACwB,OAAO,CAACT,WAAW,CAAC;IAC/BR,KAAK,CAACG,YAAY,GAAG,KAAK;IAC1B,IAAIL,YAAY,IAAID,cAAc,EAAE;MAChCE,iBAAiB,GAAG,KAAK;MACzBH,iBAAiB,CAACe,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMO,IAAI,GAAGA,CAAA,KAAM;IACfpB,YAAY,GAAG,IAAI;IACnBC,iBAAiB,GAAG,IAAI;IACxB,IAAI,CAACC,KAAK,CAACG,YAAY,EAAE;MACrBP,iBAAiB,CAACe,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMQ,QAAQ,GAAG1B,UAAU,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC7C,MAAMa,IAAI,GAAGhB,KAAK,CAACG,GAAG,CAAC;IACvBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAACG,OAAO,EAAEW,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;MAC1D,IAAI,CAACxB,YAAY,EACboB,IAAI,CAAC,CAAC;MACV,OAAOE,IAAI,CAACD,QAAQ,CAACT,OAAO,EAAEW,SAAS,EAAEC,SAAS,CAAC;IACvD,CAAC;IACD,OAAOhB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAMiB,MAAM,GAAIb,OAAO,IAAKjB,UAAU,CAACwB,OAAO,CAAEV,GAAG,IAAKH,KAAK,CAACG,GAAG,CAAC,CAACgB,MAAM,CAACb,OAAO,CAAC,CAAC;EACnF,OAAO;IAAES,QAAQ;IAAEI,MAAM;IAAEvB,KAAK;IAAEI;EAAM,CAAC;AAC7C;AAEA,SAAST,mBAAmB,EAAEF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}