{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ServerCog = createLucideIcon(\"ServerCog\", [[\"path\", {\n  d: \"M5 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-1\",\n  key: \"1qm4no\"\n}], [\"path\", {\n  d: \"M5 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-1\",\n  key: \"1lpaho\"\n}], [\"path\", {\n  d: \"M6 6h.01\",\n  key: \"1utrut\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"M12 8v1\",\n  key: \"1rj8u4\"\n}], [\"path\", {\n  d: \"M12 15v1\",\n  key: \"1ovrzm\"\n}], [\"path\", {\n  d: \"M16 12h-1\",\n  key: \"1qpdyp\"\n}], [\"path\", {\n  d: \"M9 12H8\",\n  key: \"1l15iv\"\n}], [\"path\", {\n  d: \"m15 9-.88.88\",\n  key: \"3hwatj\"\n}], [\"path\", {\n  d: \"M9.88 14.12 9 15\",\n  key: \"13ldc9\"\n}], [\"path\", {\n  d: \"m15 15-.88-.88\",\n  key: \"45priv\"\n}], [\"path\", {\n  d: \"M9.88 9.88 9 9\",\n  key: \"1ladhj\"\n}]]);\nexport { ServerCog as default };", "map": {"version": 3, "names": ["ServerCog", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\server-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ServerCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMEg0YTIgMiAwIDAgMS0yLTJWNGEyIDIgMCAwIDEgMi0yaDE2YTIgMiAwIDAgMSAyIDJ2NGEyIDIgMCAwIDEtMiAyaC0xIiAvPgogIDxwYXRoIGQ9Ik01IDE0SDRhMiAyIDAgMCAwLTIgMnY0YTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMnYtNGEyIDIgMCAwIDAtMi0yaC0xIiAvPgogIDxwYXRoIGQ9Ik02IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik02IDE4aC4wMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xMiA4djEiIC8+CiAgPHBhdGggZD0iTTEyIDE1djEiIC8+CiAgPHBhdGggZD0iTTE2IDEyaC0xIiAvPgogIDxwYXRoIGQ9Ik05IDEySDgiIC8+CiAgPHBhdGggZD0ibTE1IDktLjg4Ljg4IiAvPgogIDxwYXRoIGQ9Ik05Ljg4IDE0LjEyIDkgMTUiIC8+CiAgPHBhdGggZD0ibTE1IDE1LS44OC0uODgiIC8+CiAgPHBhdGggZD0iTTkuODggOS44OCA5IDkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/server-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ServerCog = createLucideIcon('ServerCog', [\n  [\n    'path',\n    {\n      d: 'M5 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-1',\n      key: '1qm4no',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M5 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-1',\n      key: '1lpaho',\n    },\n  ],\n  ['path', { d: 'M6 6h.01', key: '1utrut' }],\n  ['path', { d: 'M6 18h.01', key: 'uhywen' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  ['path', { d: 'M12 8v1', key: '1rj8u4' }],\n  ['path', { d: 'M12 15v1', key: '1ovrzm' }],\n  ['path', { d: 'M16 12h-1', key: '1qpdyp' }],\n  ['path', { d: 'M9 12H8', key: '1l15iv' }],\n  ['path', { d: 'm15 9-.88.88', key: '3hwatj' }],\n  ['path', { d: 'M9.88 14.12 9 15', key: '13ldc9' }],\n  ['path', { d: 'm15 15-.88-.88', key: '45priv' }],\n  ['path', { d: 'M9.88 9.88 9 9', key: '1ladhj' }],\n]);\n\nexport default ServerCog;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}