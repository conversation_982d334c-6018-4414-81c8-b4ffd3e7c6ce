{"ast": null, "code": "import { featureDefinitions } from './definitions.mjs';\nfunction loadFeatures(features) {\n  for (const key in features) {\n    featureDefinitions[key] = {\n      ...featureDefinitions[key],\n      ...features[key]\n    };\n  }\n}\nexport { loadFeatures };", "map": {"version": 3, "names": ["featureDefinitions", "loadFeatures", "features", "key"], "sources": ["C:/Users/<USER>/Desktop/projetest2/node_modules/framer-motion/dist/es/motion/features/load-features.mjs"], "sourcesContent": ["import { featureDefinitions } from './definitions.mjs';\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        featureDefinitions[key] = {\n            ...featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\nexport { loadFeatures };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,mBAAmB;AAEtD,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,KAAK,MAAMC,GAAG,IAAID,QAAQ,EAAE;IACxBF,kBAAkB,CAACG,GAAG,CAAC,GAAG;MACtB,GAAGH,kBAAkB,CAACG,GAAG,CAAC;MAC1B,GAAGD,QAAQ,CAACC,GAAG;IACnB,CAAC;EACL;AACJ;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}