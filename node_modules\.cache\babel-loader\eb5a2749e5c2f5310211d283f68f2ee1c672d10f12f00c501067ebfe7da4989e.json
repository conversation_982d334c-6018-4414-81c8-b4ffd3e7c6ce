{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst RussianRuble = createLucideIcon(\"RussianRuble\", [[\"path\", {\n  d: \"M14 11c5.333 0 5.333-8 0-8\",\n  key: \"92e629\"\n}], [\"path\", {\n  d: \"M6 11h8\",\n  key: \"1cr2u4\"\n}], [\"path\", {\n  d: \"M6 15h8\",\n  key: \"1y8f6l\"\n}], [\"path\", {\n  d: \"M9 21V3\",\n  key: \"1jd2g6\"\n}], [\"path\", {\n  d: \"M9 3h5\",\n  key: \"8bgvcw\"\n}]]);\nexport { RussianRuble as default };", "map": {"version": 3, "names": ["RussianRuble", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\russian-ruble.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RussianRuble\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTFjNS4zMzMgMCA1LjMzMy04IDAtOCIgLz4KICA8cGF0aCBkPSJNNiAxMWg4IiAvPgogIDxwYXRoIGQ9Ik02IDE1aDgiIC8+CiAgPHBhdGggZD0iTTkgMjFWMyIgLz4KICA8cGF0aCBkPSJNOSAzaDUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/russian-ruble\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RussianRuble = createLucideIcon('RussianRuble', [\n  ['path', { d: 'M14 11c5.333 0 5.333-8 0-8', key: '92e629' }],\n  ['path', { d: 'M6 11h8', key: '1cr2u4' }],\n  ['path', { d: 'M6 15h8', key: '1y8f6l' }],\n  ['path', { d: 'M9 21V3', key: '1jd2g6' }],\n  ['path', { d: 'M9 3h5', key: '8bgvcw' }],\n]);\n\nexport default RussianRuble;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}