{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst School = createLucideIcon(\"School\", [[\"path\", {\n  d: \"m4 6 8-4 8 4\",\n  key: \"1q0ilc\"\n}], [\"path\", {\n  d: \"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2\",\n  key: \"1vwozw\"\n}], [\"path\", {\n  d: \"M14 22v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v4\",\n  key: \"cpkuc4\"\n}], [\"path\", {\n  d: \"M18 5v17\",\n  key: \"1sw6gf\"\n}], [\"path\", {\n  d: \"M6 5v17\",\n  key: \"1xfsm0\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"1092wv\"\n}]]);\nexport { School as default };", "map": {"version": 3, "names": ["School", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\projetest2\\node_modules\\lucide-react\\src\\icons\\school.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name School\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNCA2IDgtNCA4IDQiIC8+CiAgPHBhdGggZD0ibTE4IDEwIDQgMnY4YTIgMiAwIDAgMS0yIDJINGEyIDIgMCAwIDEtMi0ydi04bDQtMiIgLz4KICA8cGF0aCBkPSJNMTQgMjJ2LTRhMiAyIDAgMCAwLTItMnYwYTIgMiAwIDAgMC0yIDJ2NCIgLz4KICA8cGF0aCBkPSJNMTggNXYxNyIgLz4KICA8cGF0aCBkPSJNNiA1djE3IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iOSIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/school\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst School = createLucideIcon('School', [\n  ['path', { d: 'm4 6 8-4 8 4', key: '1q0ilc' }],\n  [\n    'path',\n    { d: 'm18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2', key: '1vwozw' },\n  ],\n  ['path', { d: 'M14 22v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v4', key: 'cpkuc4' }],\n  ['path', { d: 'M18 5v17', key: '1sw6gf' }],\n  ['path', { d: 'M6 5v17', key: '1xfsm0' }],\n  ['circle', { cx: '12', cy: '9', r: '2', key: '1092wv' }],\n]);\n\nexport default School;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CACE,QACA;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAS,EAC1E,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}