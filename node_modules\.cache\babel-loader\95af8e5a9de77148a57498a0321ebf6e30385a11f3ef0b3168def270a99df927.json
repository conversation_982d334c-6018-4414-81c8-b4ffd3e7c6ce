{"ast": null, "code": "function resolveVariantFromProps(props, definition, custom, currentValues = {}, currentVelocity = {}) {\n  /**\n   * If the variant definition is a function, resolve.\n   */\n  if (typeof definition === \"function\") {\n    definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n  }\n  /**\n   * If the variant definition is a variant label, or\n   * the function returned a variant label, resolve.\n   */\n  if (typeof definition === \"string\") {\n    definition = props.variants && props.variants[definition];\n  }\n  /**\n   * At this point we've resolved both functions and variant labels,\n   * but the resolved variant label might itself have been a function.\n   * If so, resolve. This can only have returned a valid target object.\n   */\n  if (typeof definition === \"function\") {\n    definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n  }\n  return definition;\n}\nexport { resolveVariantFromProps };", "map": {"version": 3, "names": ["resolveVariantFromProps", "props", "definition", "custom", "currentV<PERSON>ues", "currentVelocity", "undefined", "variants"], "sources": ["C:/Users/<USER>/Desktop/projetest2/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs"], "sourcesContent": ["function resolveVariantFromProps(props, definition, custom, currentValues = {}, currentVelocity = {}) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n"], "mappings": "AAAA,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,aAAa,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,CAAC,CAAC,EAAE;EAClG;AACJ;AACA;EACI,IAAI,OAAOH,UAAU,KAAK,UAAU,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzG;EACA;AACJ;AACA;AACA;EACI,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;IAChCA,UAAU,GAAGD,KAAK,CAACM,QAAQ,IAAIN,KAAK,CAACM,QAAQ,CAACL,UAAU,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzG;EACA,OAAOH,UAAU;AACrB;AAEA,SAASF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}