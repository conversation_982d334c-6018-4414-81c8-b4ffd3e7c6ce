{"ast": null, "code": "function isDOMKeyframes(keyframes) {\n  return typeof keyframes === \"object\" && !Array.isArray(keyframes);\n}\nexport { isDOMKeyframes };", "map": {"version": 3, "names": ["isDOMKeyframes", "keyframes", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/projetest2/node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs"], "sourcesContent": ["function isDOMKeyframes(keyframes) {\n    return typeof keyframes === \"object\" && !Array.isArray(keyframes);\n}\n\nexport { isDOMKeyframes };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,SAAS,EAAE;EAC/B,OAAO,OAAOA,SAAS,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC;AACrE;AAEA,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}